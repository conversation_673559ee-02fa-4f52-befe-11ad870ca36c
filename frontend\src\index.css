:root {
  font-family: "Inter", "SF Pro Display", "Segoe UI", "Roboto", -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  font-weight: 400;

  /* Professional color variables */
  --pastel-lavender: #E8E2F0;
  --pastel-mint: #E0F2E7;
  --pastel-peach: #FFE8D6;
  --pastel-sky: #E3F2FD;
  --pastel-rose: #FCE4EC;
  --pastel-cream: #FFF8E1;

  --dark-lavender: #4A148C;
  --dark-mint: #1B5E20;
  --dark-peach: #E65100;
  --dark-sky: #0D47A1;
  --dark-rose: #880E4F;
  --dark-cream: #F57F17;

  --matte-black: #0A0A0A;
  --matte-charcoal: #1A1A1A;
  --matte-graphite: #2A2A2A;
  --matte-slate: #3A3A3A;

  --neon-lavender: #B39DDB;
  --neon-mint: #81C784;
  --neon-peach: #FFB74D;
  --neon-sky: #64B5F6;
  --neon-rose: #F48FB1;
  --neon-cream: #FFF176;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  min-height: 100vh;
  overflow-x: hidden;
  scroll-behavior: smooth;
}

/* Professional Custom Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: var(--matte-charcoal);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--neon-sky), var(--neon-lavender));
  border-radius: 10px;
  border: 2px solid var(--matte-charcoal);
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--neon-lavender), var(--neon-mint));
  transform: scale(1.1);
}

/* Enhanced Video Player Styles */
video {
  max-height: 70vh;
  background: var(--matte-black);
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

video:hover {
  transform: scale(1.02);
  box-shadow: 0 25px 80px rgba(100, 181, 246, 0.3);
}

/* Infinite scroll loader */
.infinite-scroll-component {
  overflow: hidden !important;
}

/* Professional Animation Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px var(--neon-sky);
  }
  50% {
    box-shadow: 0 0 20px var(--neon-sky), 0 0 30px var(--neon-lavender);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes slideInFromTop {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-180deg) scale(0.5);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Professional Animation Classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.animate-pulse {
  animation: pulse 2s ease-in-out infinite;
}

.animate-slide-in-top {
  animation: slideInFromTop 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-slide-in-bottom {
  animation: slideInFromBottom 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-rotate-in {
  animation: rotateIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.animate-bounce-in {
  animation: bounceIn 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* Professional Utility Classes */
.glass-effect {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gradient-text {
  background: linear-gradient(135deg, var(--neon-sky), var(--neon-lavender));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.gradient-text-light {
  background: linear-gradient(135deg, var(--dark-sky), var(--dark-lavender));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.shadow-glow {
  box-shadow: 0 0 20px rgba(100, 181, 246, 0.3);
}

.shadow-glow-hover:hover {
  box-shadow: 0 0 30px rgba(100, 181, 246, 0.5);
  transition: box-shadow 0.3s ease;
}

.transform-hover:hover {
  transform: translateY(-4px) scale(1.02);
  transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.border-gradient {
  border: 2px solid transparent;
  background: linear-gradient(var(--matte-charcoal), var(--matte-charcoal)) padding-box,
              linear-gradient(135deg, var(--neon-sky), var(--neon-lavender)) border-box;
}

.border-gradient-light {
  border: 2px solid transparent;
  background: linear-gradient(white, white) padding-box,
              linear-gradient(135deg, var(--dark-sky), var(--dark-lavender)) border-box;
}

/* Smooth Page Transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.4s ease, transform 0.4s ease;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(100, 181, 246, 0.3);
  border-top: 4px solid var(--neon-sky);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Stagger Animation Delays */
.stagger-1 { animation-delay: 0.1s; }
.stagger-2 { animation-delay: 0.2s; }
.stagger-3 { animation-delay: 0.3s; }
.stagger-4 { animation-delay: 0.4s; }
.stagger-5 { animation-delay: 0.5s; }

/* Responsive Design Helpers */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus Styles for Accessibility */
*:focus-visible {
  outline: 2px solid var(--neon-sky);
  outline-offset: 2px;
  border-radius: 4px;
}
