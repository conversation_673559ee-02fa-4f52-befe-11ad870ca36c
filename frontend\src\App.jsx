import { useEffect, useState, lazy, Suspense } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, Typography } from '@mui/material';
import { StrictMode } from 'react';
import CircularProgress from '@mui/material/CircularProgress';
import ThemeShowcase from './components/theme/ThemeShowcase.jsx';

// Layouts
import MainLayout from './layouts/MainLayout.jsx';
import MinimalLayout from './layouts/MinimalLayout.jsx';

// Lazy-loaded pages for better performance
import {
  LazyHome,
  LazyTrending,
  LazySubscriptions,
  LazyHistory,
  LazyWatchLater,
  LazyPlaylists,
  LazySearch,
  LazyVideoDetail,
  LazyChannel,
  LazyDashboard,
  LazyUpload,
  LazyLogin,
  LazyRegister,
  LazyForgotPassword,
  LazyResetPassword,
  preloadCriticalRoutes
} from './routes/LazyRoutes.jsx';

// Performance monitoring
import performanceMonitor from './utils/performance.js';

// Direct imports for critical components that need to load immediately
import PlaylistDetail from './pages/PlaylistDetail.jsx';
import NotFound from './pages/NotFound.jsx';
import Studio from './pages/Studio.jsx';
import Liked from './pages/Liked.jsx';
import Recommendations from './pages/Recommendations.jsx';
import Settings from './pages/Settings.jsx';
import Notifications from './pages/Notifications.jsx';
// Use the proper lazy-loaded components
// ResetPassword is now imported from LazyRoutes







// Components
import ProtectedRoute from './components/common/ProtectedRoute.jsx';
import ErrorBoundary from './components/common/ErrorBoundary.jsx';
import LoadingSpinner from './components/common/LoadingSpinner.jsx';

// Services and Context
import { useAuth } from './context/AuthContext.jsx';

// Fallback loading component
const LoadingFallback = () => (
  <Box 
    sx={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh' 
    }}
  >
    <CircularProgress />
  </Box>
);

const App = () => {
  const { user } = useAuth();
  const [initializing, setInitializing] = useState(true);


  useEffect(() => {
    // Initialize performance monitoring
    preloadCriticalRoutes();

    // Send performance report after 5 seconds
    setTimeout(() => {
      performanceMonitor.sendToAnalytics();
    }, 5000);

    // Set initializing to false since AuthContext handles auth initialization
    setInitializing(false);
  }, []);

  // Fallback to ensure loading state doesn't get stuck
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (initializing) {
        setInitializing(false);
        console.warn('Authentication initialization timed out');
      }
    }, 5000); // 5 seconds maximum loading time

    return () => clearTimeout(timeoutId);
  }, [initializing]);

  if (initializing) {
    return <LoadingSpinner />;
  }

  return (
    <Box sx={{ bgcolor: 'background.default', minHeight: '100vh' }}>
          <Routes>
            {/* Auth Routes - Minimal Layout */}
            <Route element={<MinimalLayout />}>
              <Route path="/signin" element={
                user ? <Navigate to="/" replace /> : <LazyLogin />
              } />
              <Route path="/signup" element={
                user ? <Navigate to="/" replace /> : <LazyRegister />
              } />
              <Route path="/forgot-password" element={<LazyForgotPassword />} />
              <Route path="/reset-password/:token" element={<LazyResetPassword />} />
            </Route>

            {/* Public Routes - Main Layout */}
            <Route element={<MainLayout />}>
              <Route path="/" element={
                <ErrorBoundary fallback={<div>Error loading home content</div>}>
                  <LazyHome />
                </ErrorBoundary>
              } />
              <Route path="/trending" element={
                <ErrorBoundary fallback={<div>Error loading trending content</div>}>
                  <LazyTrending />
                </ErrorBoundary>
              } />
              <Route path="/theme-showcase" element={<ThemeShowcase />} />
              <Route path="/search" element={
                <ErrorBoundary fallback={<div>Error loading search results</div>}>
                  <LazySearch />
                </ErrorBoundary>
              } />
              <Route path="/videos/:videoId" element={
                <ErrorBoundary fallback={<div>Error loading video</div>}>
                  <LazyVideoDetail />
                </ErrorBoundary>
              } />
              <Route path="/watch/:videoId" element={
                <Navigate to={(location) => `/videos/${location.pathname.split('/').pop()}`} replace />
              } />
              <Route path="/channel/:username" element={
                <ErrorBoundary fallback={<div>Error loading channel</div>}>
                  <LazyChannel />
                </ErrorBoundary>
              } />
              <Route path="/playlist/:playlistId" element={
                <ErrorBoundary fallback={<div>Error loading playlist</div>}>
                  <PlaylistDetail />
                </ErrorBoundary>
              } />

              {/* Protected Routes - Require Login */}
              <Route path="/subscriptions" element={
                <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading subscriptions</div>}>
                    <LazySubscriptions />
                  </ErrorBoundary>
                </ProtectedRoute>
              } />
              <Route path="/history" element={
                <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading history</div>}>
                    <LazyHistory />
                  </ErrorBoundary>
                </ProtectedRoute>
              } />
              <Route path="/watch-later" element={
                <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading watch later</div>}>
                    <LazyWatchLater />
                  </ErrorBoundary>
                </ProtectedRoute>
              } />
              <Route path="/playlists" element={
                <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading playlists</div>}>
                    <LazyPlaylists />
                  </ErrorBoundary>
                </ProtectedRoute>
              } />
              <Route path="/liked-videos" element={
          <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading liked videos</div>}>
                    <Liked />
                  </ErrorBoundary>
          </ProtectedRoute>
        } />
              <Route path="/recommendations" element={
          <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading recommendations</div>}>
                    <Recommendations />
                  </ErrorBoundary>
          </ProtectedRoute>
        } />
              <Route path="/settings" element={
          <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading settings</div>}>
                    <Settings />
                  </ErrorBoundary>
          </ProtectedRoute>
        } />
        <Route path="/notifications" element={
          <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading notifications</div>}>
                    <Notifications />
                  </ErrorBoundary>
          </ProtectedRoute>
        } />
              
              {/* Creator Studio Routes */}
              <Route path="/studio" element={
          <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading studio</div>}>
                    <Studio />
                  </ErrorBoundary>
          </ProtectedRoute>
        } />
              <Route path="/studio/dashboard" element={
          <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading dashboard</div>}>
                    <LazyDashboard />
                  </ErrorBoundary>
          </ProtectedRoute>
        } />
              <Route path="/studio/upload" element={
          <ProtectedRoute>
                  <ErrorBoundary fallback={<div>Error loading upload</div>}>
                    <LazyUpload />
                  </ErrorBoundary>
          </ProtectedRoute>
        } />
        
              {/* Not Found */}
        <Route path="*" element={<NotFound />} />
            </Route>
      </Routes>
    </Box>
  );
};

export default App;
