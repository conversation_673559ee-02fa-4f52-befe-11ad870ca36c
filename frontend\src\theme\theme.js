import { createTheme } from '@mui/material/styles';

/**
 * Professional VibeTube Theme with Soft Pastel Colors & Matte Black Design
 * Dark Mode: Matte black backgrounds with soft neon accents
 * Light Mode: Beautiful pastel colors with dark text variants for calm aesthetic
 * @param {boolean} darkMode - Whether to use dark mode
 * @returns {Object} MUI theme object with professional animations and effects
 */
export const createAppTheme = (darkMode = false) => {
  // Professional Color Palette
  const colors = {
    // Soft pastel colors for light mode
    pastel: {
      lavender: '#E8E2F0',      // Soft purple background
      mint: '#E0F2E7',          // Soft green background
      peach: '#FFE8D6',         // Soft orange background
      sky: '#E3F2FD',           // Soft blue background
      rose: '#FCE4EC',          // Soft pink background
      cream: '#FFF8E1',         // Soft yellow background
      sage: '#F1F8E9',          // Soft lime background
      powder: '#F3E5F5',        // Soft violet background
    },
    // Dark variants for text in light mode
    darkVariants: {
      lavender: '#4A148C',      // Deep purple text
      mint: '#1B5E20',          // Deep green text
      peach: '#E65100',         // Deep orange text
      sky: '#0D47A1',           // Deep blue text
      rose: '#880E4F',          // Deep pink text
      cream: '#F57F17',         // Deep yellow text
      sage: '#33691E',          // Deep lime text
      powder: '#4A148C',        // Deep violet text
    },
    // Matte colors for dark mode
    matte: {
      black: '#0A0A0A',         // True matte black
      charcoal: '#1A1A1A',      // Slightly lighter black
      graphite: '#2A2A2A',      // Medium dark gray
      slate: '#3A3A3A',         // Lighter gray for contrast
    },
    // Soft neon accents for dark mode
    neon: {
      lavender: '#B39DDB',      // Soft purple glow
      mint: '#81C784',          // Soft green glow
      peach: '#FFB74D',         // Soft orange glow
      sky: '#64B5F6',           // Soft blue glow
      rose: '#F48FB1',          // Soft pink glow
      cream: '#FFF176',         // Soft yellow glow
    }
  };

  return createTheme({
    palette: {
      mode: darkMode ? 'dark' : 'light',
      primary: {
        main: darkMode ? colors.neon.sky : colors.darkVariants.sky,
        light: darkMode ? '#90CAF9' : '#42A5F5',
        dark: darkMode ? '#1976D2' : '#0D47A1',
        contrastText: darkMode ? colors.matte.black : '#FFFFFF',
      },
      secondary: {
        main: darkMode ? colors.neon.lavender : colors.darkVariants.lavender,
        light: darkMode ? '#CE93D8' : '#BA68C8',
        dark: darkMode ? '#7B1FA2' : '#4A148C',
        contrastText: darkMode ? colors.matte.black : '#FFFFFF',
      },
      tertiary: {
        main: darkMode ? colors.neon.mint : colors.darkVariants.mint,
        light: darkMode ? '#A5D6A7' : '#66BB6A',
        dark: darkMode ? '#388E3C' : '#1B5E20',
        contrastText: darkMode ? colors.matte.black : '#FFFFFF',
      },
      background: {
        default: darkMode ? colors.matte.black : colors.pastel.lavender,
        paper: darkMode ? colors.matte.charcoal : colors.pastel.sky,
        subtle: darkMode ? colors.matte.graphite : colors.pastel.mint,
        accent: darkMode ? colors.matte.slate : colors.pastel.rose,
        card: darkMode ? colors.matte.charcoal : colors.pastel.cream,
      },
      text: {
        primary: darkMode ? '#FFFFFF' : colors.darkVariants.lavender,
        secondary: darkMode ? 'rgba(255, 255, 255, 0.8)' : colors.darkVariants.sky,
        disabled: darkMode ? 'rgba(255, 255, 255, 0.5)' : 'rgba(74, 20, 140, 0.5)',
        hint: darkMode ? 'rgba(255, 255, 255, 0.6)' : colors.darkVariants.mint,
      },
      divider: darkMode ? 'rgba(255, 255, 255, 0.08)' : 'rgba(74, 20, 140, 0.08)',
      error: {
        main: darkMode ? '#FF6B6B' : '#D32F2F',
        light: darkMode ? '#FF8A80' : '#EF5350',
        dark: darkMode ? '#D32F2F' : '#C62828',
      },
      warning: {
        main: darkMode ? colors.neon.peach : colors.darkVariants.peach,
        light: darkMode ? '#FFCC02' : '#FF9800',
        dark: darkMode ? '#F57C00' : '#E65100',
      },
      info: {
        main: darkMode ? colors.neon.sky : colors.darkVariants.sky,
        light: darkMode ? '#64B5F6' : '#42A5F5',
        dark: darkMode ? '#1976D2' : '#0D47A1',
      },
      success: {
        main: darkMode ? colors.neon.mint : colors.darkVariants.mint,
        light: darkMode ? '#81C784' : '#66BB6A',
        dark: darkMode ? '#388E3C' : '#1B5E20',
      },
    },
    typography: {
      fontFamily: '"Inter", "SF Pro Display", "Segoe UI", "Roboto", -apple-system, BlinkMacSystemFont, sans-serif',
      fontWeightLight: 300,
      fontWeightRegular: 400,
      fontWeightMedium: 500,
      fontWeightBold: 600,
      fontWeightExtraBold: 700,
      h1: {
        fontSize: '3rem',
        fontWeight: 700,
        lineHeight: 1.2,
        letterSpacing: '-0.02em',
        background: darkMode
          ? `linear-gradient(135deg, ${colors.neon.sky}, ${colors.neon.lavender})`
          : `linear-gradient(135deg, ${colors.darkVariants.sky}, ${colors.darkVariants.lavender})`,
        backgroundClip: 'text',
        WebkitBackgroundClip: 'text',
        WebkitTextFillColor: 'transparent',
        textShadow: darkMode ? '0 0 30px rgba(100, 181, 246, 0.3)' : 'none',
      },
      h2: {
        fontSize: '2.5rem',
        fontWeight: 600,
        lineHeight: 1.3,
        letterSpacing: '-0.01em',
        color: darkMode ? colors.neon.lavender : colors.darkVariants.lavender,
      },
      h3: {
        fontSize: '2rem',
        fontWeight: 600,
        lineHeight: 1.4,
        letterSpacing: '-0.005em',
        color: darkMode ? colors.neon.mint : colors.darkVariants.mint,
      },
      h4: {
        fontSize: '1.75rem',
        fontWeight: 600,
        lineHeight: 1.4,
        color: darkMode ? colors.neon.sky : colors.darkVariants.sky,
      },
      h5: {
        fontSize: '1.5rem',
        fontWeight: 500,
        lineHeight: 1.5,
        color: darkMode ? colors.neon.rose : colors.darkVariants.rose,
      },
      h6: {
        fontSize: '1.25rem',
        fontWeight: 500,
        lineHeight: 1.5,
        color: darkMode ? colors.neon.peach : colors.darkVariants.peach,
      },
      subtitle1: {
        fontSize: '1.125rem',
        fontWeight: 500,
        lineHeight: 1.6,
        letterSpacing: '0.01em',
      },
      subtitle2: {
        fontSize: '1rem',
        fontWeight: 500,
        lineHeight: 1.6,
        letterSpacing: '0.01em',
      },
      body1: {
        fontSize: '1rem',
        fontWeight: 400,
        lineHeight: 1.7,
        letterSpacing: '0.005em',
      },
      body2: {
        fontSize: '0.875rem',
        fontWeight: 400,
        lineHeight: 1.6,
        letterSpacing: '0.01em',
      },
      button: {
        fontSize: '0.875rem',
        fontWeight: 600,
        textTransform: 'none',
        letterSpacing: '0.02em',
      },
      caption: {
        fontSize: '0.75rem',
        fontWeight: 400,
        lineHeight: 1.5,
        letterSpacing: '0.03em',
      },
      overline: {
        fontSize: '0.75rem',
        fontWeight: 600,
        textTransform: 'uppercase',
        letterSpacing: '0.08em',
      },
    },
    shape: {
      borderRadius: 16,
    },
    spacing: 8, // Base spacing unit
    transitions: {
      duration: {
        shortest: 120,
        shorter: 180,
        short: 250,
        standard: 300,
        complex: 400,
        enteringScreen: 280,
        leavingScreen: 220,
        slow: 500,
        verySlow: 800,
      },
      easing: {
        // Professional easing curves for smooth animations
        easeInOut: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',      // Smooth and natural
        easeOut: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',        // Gentle deceleration
        easeIn: 'cubic-bezier(0.55, 0.055, 0.675, 0.19)',       // Smooth acceleration
        sharp: 'cubic-bezier(0.4, 0, 0.6, 1)',                  // Quick and precise
        bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',       // Playful bounce
        elastic: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',     // Elastic feel
        anticipate: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',     // Natural anticipation
      },
    },
    // Custom animation keyframes
    animations: {
      fadeIn: {
        from: { opacity: 0, transform: 'translateY(20px)' },
        to: { opacity: 1, transform: 'translateY(0)' },
      },
      slideIn: {
        from: { transform: 'translateX(-100%)' },
        to: { transform: 'translateX(0)' },
      },
      scaleIn: {
        from: { transform: 'scale(0.8)', opacity: 0 },
        to: { transform: 'scale(1)', opacity: 1 },
      },
      glow: {
        '0%': { boxShadow: `0 0 5px ${darkMode ? colors.neon.sky : colors.darkVariants.sky}` },
        '50%': { boxShadow: `0 0 20px ${darkMode ? colors.neon.sky : colors.darkVariants.sky}` },
        '100%': { boxShadow: `0 0 5px ${darkMode ? colors.neon.sky : colors.darkVariants.sky}` },
      },
      float: {
        '0%, 100%': { transform: 'translateY(0px)' },
        '50%': { transform: 'translateY(-10px)' },
      },
      shimmer: {
        '0%': { backgroundPosition: '-200% 0' },
        '100%': { backgroundPosition: '200% 0' },
      },
    },
    components: {
      MuiButton: {
        styleOverrides: {
          root: {
            borderRadius: 28,
            textTransform: 'none',
            fontWeight: 600,
            padding: '12px 32px',
            position: 'relative',
            overflow: 'hidden',
            transition: 'all 0.25s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: '-100%',
              width: '100%',
              height: '100%',
              background: darkMode
                ? 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent)'
                : 'linear-gradient(90deg, transparent, rgba(0, 0, 0, 0.1), transparent)',
              transition: 'left 0.6s ease',
            },
            '&:hover': {
              transform: 'translateY(-3px) scale(1.02)',
              boxShadow: darkMode
                ? '0 12px 40px rgba(100, 181, 246, 0.4)'
                : '0 12px 40px rgba(13, 71, 161, 0.3)',
              '&::before': {
                left: '100%',
              },
            },
            '&:active': {
              transform: 'translateY(-1px) scale(0.98)',
              transition: 'all 0.1s ease',
            },
          },
          contained: {
            background: darkMode
              ? `linear-gradient(135deg, ${colors.neon.sky}, ${colors.neon.lavender})`
              : `linear-gradient(135deg, ${colors.darkVariants.sky}, ${colors.darkVariants.lavender})`,
            boxShadow: darkMode
              ? '0 8px 32px rgba(100, 181, 246, 0.3)'
              : '0 8px 32px rgba(13, 71, 161, 0.2)',
            '&:hover': {
              background: darkMode
                ? `linear-gradient(135deg, ${colors.neon.lavender}, ${colors.neon.mint})`
                : `linear-gradient(135deg, ${colors.darkVariants.lavender}, ${colors.darkVariants.mint})`,
              boxShadow: darkMode
                ? '0 16px 48px rgba(179, 157, 219, 0.4)'
                : '0 16px 48px rgba(74, 20, 140, 0.3)',
            },
          },
          outlined: {
            borderWidth: 2,
            borderColor: darkMode ? colors.neon.sky : colors.darkVariants.sky,
            color: darkMode ? colors.neon.sky : colors.darkVariants.sky,
            '&:hover': {
              borderColor: darkMode ? colors.neon.lavender : colors.darkVariants.lavender,
              backgroundColor: darkMode
                ? 'rgba(179, 157, 219, 0.1)'
                : 'rgba(74, 20, 140, 0.05)',
              color: darkMode ? colors.neon.lavender : colors.darkVariants.lavender,
            },
          },
          text: {
            color: darkMode ? colors.neon.sky : colors.darkVariants.sky,
            '&:hover': {
              backgroundColor: darkMode
                ? 'rgba(100, 181, 246, 0.1)'
                : 'rgba(13, 71, 161, 0.05)',
              color: darkMode ? colors.neon.lavender : colors.darkVariants.lavender,
            },
          },
        },
      },
      MuiPaper: {
        styleOverrides: {
          root: {
            backgroundImage: 'none',
            backgroundColor: darkMode ? colors.matte.charcoal : colors.pastel.cream,
            borderRadius: 20,
            border: darkMode
              ? `1px solid rgba(255, 255, 255, 0.05)`
              : `1px solid rgba(74, 20, 140, 0.08)`,
            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: darkMode
                ? '0 20px 60px rgba(0, 0, 0, 0.4)'
                : '0 20px 60px rgba(74, 20, 140, 0.1)',
            },
          },
          elevation1: {
            boxShadow: darkMode
              ? '0 4px 20px rgba(0, 0, 0, 0.3)'
              : '0 4px 20px rgba(74, 20, 140, 0.08)',
          },
          elevation2: {
            boxShadow: darkMode
              ? '0 8px 30px rgba(0, 0, 0, 0.35)'
              : '0 8px 30px rgba(74, 20, 140, 0.12)',
          },
          elevation3: {
            boxShadow: darkMode
              ? '0 12px 40px rgba(0, 0, 0, 0.4)'
              : '0 12px 40px rgba(74, 20, 140, 0.15)',
          },
        },
      },
      MuiDrawer: {
        styleOverrides: {
          paper: {
            backgroundColor: darkMode ? colors.matte.black : colors.pastel.lavender,
            borderRight: darkMode
              ? `1px solid rgba(255, 255, 255, 0.08)`
              : `1px solid rgba(74, 20, 140, 0.12)`,
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
          },
        },
      },
      MuiAppBar: {
        styleOverrides: {
          root: {
            backgroundColor: darkMode
              ? `${colors.matte.black}CC` // Semi-transparent
              : `${colors.pastel.lavender}F0`,
            color: darkMode ? '#FFFFFF' : colors.darkVariants.lavender,
            boxShadow: 'none',
            borderBottom: darkMode
              ? `1px solid rgba(255, 255, 255, 0.08)`
              : `1px solid rgba(74, 20, 140, 0.12)`,
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          },
        },
      },
      MuiList: {
        styleOverrides: {
          root: {
            padding: '8px',
            backgroundColor: 'transparent',
          },
        },
      },
      MuiListItem: {
        styleOverrides: {
          root: {
            borderRadius: 12,
            margin: '2px 0',
            transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            '&:hover': {
              backgroundColor: darkMode
                ? 'rgba(100, 181, 246, 0.1)'
                : 'rgba(74, 20, 140, 0.05)',
              transform: 'translateX(8px)',
            },
            '&.Mui-selected': {
              backgroundColor: darkMode
                ? 'rgba(100, 181, 246, 0.15)'
                : 'rgba(74, 20, 140, 0.08)',
              borderLeft: `4px solid ${darkMode ? colors.neon.sky : colors.darkVariants.sky}`,
              '&:hover': {
                backgroundColor: darkMode
                  ? 'rgba(100, 181, 246, 0.2)'
                  : 'rgba(74, 20, 140, 0.12)',
              },
            },
          },
        },
      },
      MuiTooltip: {
        styleOverrides: {
          tooltip: {
            backgroundColor: darkMode ? colors.matte.slate : colors.darkVariants.lavender,
            color: darkMode ? '#FFFFFF' : '#FFFFFF',
            fontSize: '0.75rem',
            borderRadius: 8,
            padding: '8px 12px',
            boxShadow: darkMode
              ? '0 8px 32px rgba(0, 0, 0, 0.4)'
              : '0 8px 32px rgba(74, 20, 140, 0.2)',
            backdropFilter: 'blur(10px)',
            WebkitBackdropFilter: 'blur(10px)',
          },
          arrow: {
            color: darkMode ? colors.matte.slate : colors.darkVariants.lavender,
          },
        },
      },
      MuiCard: {
        styleOverrides: {
          root: {
            backgroundColor: darkMode ? colors.matte.charcoal : colors.pastel.sky,
            borderRadius: 24,
            border: darkMode
              ? `1px solid rgba(255, 255, 255, 0.05)`
              : `1px solid rgba(74, 20, 140, 0.08)`,
            transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              height: '2px',
              background: darkMode
                ? `linear-gradient(90deg, ${colors.neon.sky}, ${colors.neon.lavender}, ${colors.neon.mint})`
                : `linear-gradient(90deg, ${colors.darkVariants.sky}, ${colors.darkVariants.lavender}, ${colors.darkVariants.mint})`,
              opacity: 0,
              transition: 'opacity 0.3s ease',
            },
            '&:hover': {
              transform: 'translateY(-8px) scale(1.02)',
              boxShadow: darkMode
                ? '0 20px 60px rgba(0, 0, 0, 0.4)'
                : '0 20px 60px rgba(74, 20, 140, 0.15)',
              '&::before': {
                opacity: 1,
              },
            },
          },
        },
      },
      MuiTextField: {
        styleOverrides: {
          root: {
            '& .MuiOutlinedInput-root': {
              borderRadius: 16,
              backgroundColor: darkMode
                ? 'rgba(255, 255, 255, 0.02)'
                : 'rgba(255, 255, 255, 0.8)',
              transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
              '& fieldset': {
                borderColor: darkMode
                  ? 'rgba(255, 255, 255, 0.1)'
                  : 'rgba(74, 20, 140, 0.2)',
                borderWidth: 2,
              },
              '&:hover': {
                transform: 'translateY(-2px)',
                '& fieldset': {
                  borderColor: darkMode ? colors.neon.sky : colors.darkVariants.sky,
                },
              },
              '&.Mui-focused': {
                transform: 'translateY(-4px)',
                boxShadow: darkMode
                  ? '0 8px 32px rgba(100, 181, 246, 0.3)'
                  : '0 8px 32px rgba(13, 71, 161, 0.2)',
                '& fieldset': {
                  borderColor: darkMode ? colors.neon.lavender : colors.darkVariants.lavender,
                },
              },
            },
          },
        },
      },
      MuiChip: {
        styleOverrides: {
          root: {
            borderRadius: 20,
            fontWeight: 500,
            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            backgroundColor: darkMode
              ? 'rgba(100, 181, 246, 0.1)'
              : 'rgba(74, 20, 140, 0.08)',
            color: darkMode ? colors.neon.sky : colors.darkVariants.sky,
            border: darkMode
              ? `1px solid rgba(100, 181, 246, 0.2)`
              : `1px solid rgba(74, 20, 140, 0.15)`,
            '&:hover': {
              transform: 'scale(1.05) translateY(-2px)',
              backgroundColor: darkMode
                ? 'rgba(179, 157, 219, 0.15)'
                : 'rgba(74, 20, 140, 0.12)',
              boxShadow: darkMode
                ? '0 8px 25px rgba(100, 181, 246, 0.3)'
                : '0 8px 25px rgba(74, 20, 140, 0.2)',
            },
          },
        },
      },
      MuiIconButton: {
        styleOverrides: {
          root: {
            borderRadius: 16,
            transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            '&:hover': {
              transform: 'scale(1.1) rotate(5deg)',
              backgroundColor: darkMode
                ? 'rgba(100, 181, 246, 0.1)'
                : 'rgba(74, 20, 140, 0.08)',
              boxShadow: darkMode
                ? '0 8px 25px rgba(100, 181, 246, 0.3)'
                : '0 8px 25px rgba(74, 20, 140, 0.2)',
            },
            '&:active': {
              transform: 'scale(0.95)',
            },
          },
        },
      },
      // Additional component overrides for enhanced aesthetics
      MuiDialog: {
        styleOverrides: {
          paper: {
            borderRadius: 24,
            backgroundColor: darkMode ? colors.matte.charcoal : colors.pastel.cream,
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: darkMode
              ? `1px solid rgba(255, 255, 255, 0.1)`
              : `1px solid rgba(74, 20, 140, 0.1)`,
          },
        },
      },
      MuiMenu: {
        styleOverrides: {
          paper: {
            borderRadius: 16,
            backgroundColor: darkMode
              ? `${colors.matte.charcoal}F0`
              : `${colors.pastel.cream}F0`,
            backdropFilter: 'blur(20px)',
            WebkitBackdropFilter: 'blur(20px)',
            border: darkMode
              ? `1px solid rgba(255, 255, 255, 0.08)`
              : `1px solid rgba(74, 20, 140, 0.08)`,
            boxShadow: darkMode
              ? '0 16px 48px rgba(0, 0, 0, 0.4)'
              : '0 16px 48px rgba(74, 20, 140, 0.15)',
          },
        },
      },
      MuiMenuItem: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            margin: '2px 8px',
            transition: 'all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
            '&:hover': {
              backgroundColor: darkMode
                ? 'rgba(100, 181, 246, 0.1)'
                : 'rgba(74, 20, 140, 0.08)',
              transform: 'translateX(4px)',
            },
          },
        },
      },
      MuiSwitch: {
        styleOverrides: {
          root: {
            '& .MuiSwitch-switchBase': {
              '&.Mui-checked': {
                color: darkMode ? colors.neon.mint : colors.darkVariants.mint,
                '& + .MuiSwitch-track': {
                  backgroundColor: darkMode ? colors.neon.mint : colors.darkVariants.mint,
                },
              },
            },
          },
        },
      },
      MuiLinearProgress: {
        styleOverrides: {
          root: {
            borderRadius: 8,
            height: 8,
            backgroundColor: darkMode
              ? 'rgba(255, 255, 255, 0.1)'
              : 'rgba(74, 20, 140, 0.1)',
          },
          bar: {
            borderRadius: 8,
            background: darkMode
              ? `linear-gradient(90deg, ${colors.neon.sky}, ${colors.neon.lavender})`
              : `linear-gradient(90deg, ${colors.darkVariants.sky}, ${colors.darkVariants.lavender})`,
          },
        },
      },
      MuiCircularProgress: {
        styleOverrides: {
          root: {
            color: darkMode ? colors.neon.sky : colors.darkVariants.sky,
          },
        },
      },
    },
  });
};

export default createAppTheme;