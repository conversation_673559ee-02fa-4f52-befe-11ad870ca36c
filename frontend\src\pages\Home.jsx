import { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Container,
  Grid,
  Card,
  CardContent,
  Button,
  CircularProgress
} from '@mui/material';
import TrendingUpIcon from '@mui/icons-material/TrendingUp';
import RefreshIcon from '@mui/icons-material/Refresh';

const Home = () => {
  const [loading, setLoading] = useState(true);
  const [videos, setVideos] = useState([]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return (
      <Container maxWidth="xl" sx={{ py: 4, textAlign: 'center' }}>
        <CircularProgress size={60} />
        <Typography variant="h6" sx={{ mt: 2 }}>
          Loading amazing videos...
        </Typography>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography
          variant="h2"
          component="h1"
          gutterBottom
          className="animate-fade-in-up"
          sx={{
            fontWeight: 700,
            background: 'linear-gradient(135deg, #64B5F6, #B39DDB)',
            backgroundClip: 'text',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
          }}
        >
          <TrendingUpIcon sx={{ fontSize: 48, mr: 1, verticalAlign: 'middle' }} />
          Welcome to VibeTube
        </Typography>
        <Typography variant="h5" color="text.secondary" className="animate-fade-in-up stagger-1">
          Discover amazing videos from creators around the world
        </Typography>
      </Box>

      <Grid container spacing={3}>
        {[1, 2, 3, 4, 5, 6].map((item) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={item}>
            <Card className="transform-hover" sx={{ height: '100%' }}>
              <Box
                sx={{
                  height: 200,
                  bgcolor: 'background.subtle',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  background: 'linear-gradient(135deg, #E3F2FD, #F3E5F5)',
                }}
              >
                <Typography variant="h6" color="text.secondary">
                  Video Placeholder {item}
                </Typography>
              </Box>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Sample Video Title {item}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  This is a sample video description showcasing the new beautiful theme.
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box sx={{ textAlign: 'center', mt: 6 }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<RefreshIcon />}
          sx={{ borderRadius: 3, px: 4, py: 1.5 }}
        >
          Load More Videos
        </Button>
      </Box>
    </Container>
  );
};

export default Home;