import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Paper,
  LinearProgress,
  CircularProgress,
  Switch,
  FormControlLabel,
  Tooltip,
  Fab
} from '@mui/material';
import {
  <PERSON><PERSON>rrow,
  <PERSON>,
  Share,
  <PERSON>V<PERSON>,
  <PERSON>d,
  <PERSON>,
  Palette
} from '@mui/icons-material';
import { useTheme as useMuiTheme } from '@mui/material/styles';
import { useTheme } from '../../context/ThemeContext';

/**
 * Theme Showcase Component - Demonstrates the new pastel theme and animations
 */
const ThemeShowcase = () => {
  const muiTheme = useMuiTheme();
  const { darkMode, toggleDarkMode } = useTheme();
  const [progress, setProgress] = useState(65);
  const [liked, setLiked] = useState(false);

  const handleProgressChange = () => {
    setProgress(prev => prev >= 100 ? 0 : prev + 25);
  };

  return (
    <Box sx={{ p: 4, maxWidth: 1200, mx: 'auto' }}>
      {/* Header */}
      <Box sx={{ textAlign: 'center', mb: 6 }}>
        <Typography 
          variant="h1" 
          className="animate-fade-in-up"
          sx={{ mb: 2 }}
        >
          VibeTube Theme Showcase
        </Typography>
        <Typography 
          variant="h5" 
          color="text.secondary"
          className="animate-fade-in-up stagger-1"
          sx={{ mb: 4 }}
        >
          Professional Pastel Design with Smooth Animations
        </Typography>
        
        <FormControlLabel
          control={
            <Switch
              checked={darkMode}
              onChange={toggleDarkMode}
              color="primary"
            />
          }
          label={`${darkMode ? 'Dark' : 'Light'} Mode`}
          className="animate-scale-in stagger-2"
        />
      </Box>

      {/* Color Palette Demo */}
      <Card className="animate-fade-in-up stagger-3" sx={{ mb: 4 }}>
        <CardContent>
          <Typography variant="h4" gutterBottom>
            Color Palette
          </Typography>
          <Grid container spacing={2}>
            {['primary', 'secondary', 'tertiary', 'error', 'warning', 'info', 'success'].map((color, index) => (
              <Grid item xs={12} sm={6} md={3} key={color}>
                <Paper
                  className={`animate-scale-in stagger-${index + 1}`}
                  sx={{
                    p: 2,
                    textAlign: 'center',
                    bgcolor: `${color}.main`,
                    color: `${color}.contrastText`,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'scale(1.05) rotate(2deg)',
                      boxShadow: 6
                    }
                  }}
                >
                  <Typography variant="h6" sx={{ textTransform: 'capitalize' }}>
                    {color}
                  </Typography>
                </Paper>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Interactive Components */}
      <Grid container spacing={4}>
        {/* Buttons Demo */}
        <Grid item xs={12} md={6}>
          <Card className="animate-fade-in-left">
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Interactive Buttons
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button variant="contained" size="large" startIcon={<PlayArrow />}>
                  Play Video
                </Button>
                <Button variant="outlined" size="large" startIcon={<Share />}>
                  Share Content
                </Button>
                <Button variant="text" size="large" startIcon={<Favorite />}>
                  Add to Favorites
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Progress & Loading */}
        <Grid item xs={12} md={6}>
          <Card className="animate-fade-in-right">
            <CardContent>
              <Typography variant="h5" gutterBottom>
                Progress & Loading
              </Typography>
              <Box sx={{ mb: 3 }}>
                <Typography variant="body2" gutterBottom>
                  Video Progress: {progress}%
                </Typography>
                <LinearProgress 
                  variant="determinate" 
                  value={progress}
                  sx={{ mb: 2 }}
                />
                <Button onClick={handleProgressChange} size="small">
                  Update Progress
                </Button>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <CircularProgress size={40} />
                <Typography>Loading content...</Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Video Card Demo */}
        <Grid item xs={12} md={8}>
          <Card className="animate-fade-in-up transform-hover">
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                <Box
                  sx={{
                    width: 200,
                    height: 120,
                    bgcolor: 'background.subtle',
                    borderRadius: 2,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    position: 'relative',
                    overflow: 'hidden'
                  }}
                  className="animate-shimmer"
                >
                  <PlayArrow sx={{ fontSize: 48, color: 'primary.main' }} />
                  <Chip
                    label="12:34"
                    size="small"
                    sx={{
                      position: 'absolute',
                      bottom: 8,
                      right: 8,
                      bgcolor: 'rgba(0, 0, 0, 0.7)',
                      color: 'white'
                    }}
                  />
                </Box>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="h6" gutterBottom>
                    Amazing Video Title with Professional Theme
                  </Typography>
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    Channel Name • 1.2M views • 2 days ago
                  </Typography>
                  <Typography variant="body2" sx={{ mb: 2 }}>
                    This video demonstrates the beautiful new pastel theme with smooth animations and professional design elements.
                  </Typography>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Chip label="Technology" size="small" />
                    <Chip label="Design" size="small" />
                    <Chip label="UI/UX" size="small" />
                  </Box>
                </Box>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  <Tooltip title="Like this video">
                    <IconButton 
                      onClick={() => setLiked(!liked)}
                      color={liked ? 'error' : 'default'}
                      className="animate-pulse"
                    >
                      <Favorite />
                    </IconButton>
                  </Tooltip>
                  <IconButton>
                    <Share />
                  </IconButton>
                  <IconButton>
                    <MoreVert />
                  </IconButton>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Animation Demo */}
        <Grid item xs={12} md={4}>
          <Card className="animate-bounce-in">
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h5" gutterBottom>
                Animations
              </Typography>
              <Box className="animate-float" sx={{ mb: 2 }}>
                <Palette sx={{ fontSize: 64, color: 'primary.main' }} />
              </Box>
              <Typography variant="body2" gutterBottom>
                Smooth, professional animations enhance user experience
              </Typography>
              <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1, mt: 2 }}>
                <Fab size="small" color="primary" className="animate-glow">
                  <Star />
                </Fab>
                <Fab size="small" color="secondary">
                  <Add />
                </Fab>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Typography Showcase */}
      <Card className="animate-fade-in-up" sx={{ mt: 4 }}>
        <CardContent>
          <Typography variant="h4" gutterBottom>
            Typography Scale
          </Typography>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Typography variant="h1" gutterBottom>Heading 1</Typography>
              <Typography variant="h2" gutterBottom>Heading 2</Typography>
              <Typography variant="h3" gutterBottom>Heading 3</Typography>
              <Typography variant="h4" gutterBottom>Heading 4</Typography>
              <Typography variant="h5" gutterBottom>Heading 5</Typography>
              <Typography variant="h6" gutterBottom>Heading 6</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <Typography variant="subtitle1" gutterBottom>Subtitle 1</Typography>
              <Typography variant="subtitle2" gutterBottom>Subtitle 2</Typography>
              <Typography variant="body1" gutterBottom>Body 1 - Regular text content</Typography>
              <Typography variant="body2" gutterBottom>Body 2 - Secondary text content</Typography>
              <Typography variant="caption" display="block" gutterBottom>Caption text</Typography>
              <Typography variant="overline" display="block">Overline text</Typography>
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ThemeShowcase;
