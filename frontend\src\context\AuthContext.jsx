import React, { createContext, useState, useContext, useEffect } from 'react';
import { authService } from '../services/auth.service';

// Create Auth Context
const AuthContext = createContext();

/**
 * Auth Provider Component
 * @param {Object} props - Component props
 * @param {React.ReactNode} props.children - Child components
 */
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Initialize auth state on mount
  useEffect(() => {
    let isMounted = true;
    const initAuth = async () => {
      try {
        // Check for token
        const token = localStorage.getItem('accessToken');
        const storedUser = localStorage.getItem('user');



        if (!token) {
          if (isMounted) {
            setLoading(false);
          }
          return;
        }

        // First, restore user from localStorage if available
        if (storedUser && isMounted) {
          try {
            const userData = JSON.parse(storedUser);
            setUser(userData);
          } catch (parseError) {
            console.error('Error parsing stored user data:', parseError);
            localStorage.removeItem('user');
          }
        }

        // Then validate with API and update if needed
        try {
          const response = await authService.getCurrentUser();
          if (isMounted && response?.data) {
            setUser(response.data);
            // Update localStorage with fresh user data
            localStorage.setItem('user', JSON.stringify(response.data));
          }
        } catch (apiError) {
          console.error('API validation failed:', apiError);
          // If API fails but we have stored user data, keep the user logged in
          // Only clear auth if it's a 401/403 (unauthorized)
          if (apiError.response?.status === 401 || apiError.response?.status === 403) {
            if (isMounted) {
              setUser(null);
              localStorage.removeItem('accessToken');
              localStorage.removeItem('refreshToken');
              localStorage.removeItem('user');
            }
          }
          // For other errors (network, server), keep the stored user data
        }
      } catch (err) {
        if (isMounted) {
          console.error('Auth initialization error:', err);
          // Only clear auth data if it's an authentication error
          if (err.response?.status === 401 || err.response?.status === 403) {
            setUser(null);
            localStorage.removeItem('accessToken');
            localStorage.removeItem('refreshToken');
            localStorage.removeItem('user');
          }
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    initAuth();

    // Cleanup function for StrictMode compatibility
    return () => {
      isMounted = false;
    };
  }, []);

  /**
   * Login user with credentials
   * @param {Object} credentials - Login credentials
   * @param {string} credentials.email - User email
   * @param {string} credentials.password - User password
   */
  const login = async (credentials) => {
    try {
      setLoading(true);
      setError(null);
      const response = await authService.login(credentials);

      // Store tokens and user data
      localStorage.setItem('accessToken', response.data.accessToken);
      if (response.data.refreshToken) {
        localStorage.setItem('refreshToken', response.data.refreshToken);
      }
      localStorage.setItem('user', JSON.stringify(response.data.user));

      setUser(response.data.user);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Login failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Register new user
   * @param {Object} userData - User registration data
   */
  const register = async (userData) => {
    try {
      setLoading(true);
      setError(null);
      const response = await authService.register(userData);
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Registration failed');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  /**
   * Logout current user
   */
  const logout = async () => {
    try {
      setLoading(true);
      await authService.logout();
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      setUser(null);
    } catch (err) {
      console.error('Logout error:', err);
      // Even if logout API fails, clear local state
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('user');
      setUser(null);
    } finally {
      setLoading(false);
    }
  };

  /**
   * Update user profile
   * @param {Object} userData - User profile data to update
   */
  const updateProfile = async (userData) => {
    try {
      setLoading(true);
      const response = await authService.updateProfile(userData);
      setUser(prev => ({ ...prev, ...response.data.user }));
      return response.data;
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to update profile');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  // Auth context value
  const value = {
    user,
    loading,
    error,
    login,
    register,
    logout,
    updateProfile,
    isAuthenticated: !!user,
  };



  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

/**
 * Hook to use the auth context
 * @returns {Object} Auth context
 */
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext; 