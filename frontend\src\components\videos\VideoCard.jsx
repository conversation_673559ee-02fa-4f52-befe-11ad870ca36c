import React, { useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Card,
  CardActionArea,
  CardContent,
  CardMedia,
  Typography,
  Box,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Skeleton,
  Stack,
  Chip,
  useTheme
} from '@mui/material';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import AddToPlaylistIcon from '@mui/icons-material/PlaylistAdd';
import WatchLaterIcon from '@mui/icons-material/WatchLater';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CircularProgress from '@mui/material/CircularProgress';
import { formatRelativeTime, formatDuration, formatCount } from '../../utils/formatters.js';
import { useAuth } from '../../context/AuthContext';
import AddToPlaylistDialog from '../playlists/AddToPlaylistDialog';
import { STATIC_URL } from '../../services/api';
import { formatVideoUrls } from '../../services/video.service';

/**
 * VideoCard component - Displays a video thumbnail and metadata in various layouts
 * 
 * @param {Object} props
 * @param {Object} props.video - Video data
 * @param {boolean} props.horizontal - Whether to display the card horizontally
 * @param {string} props.thumbnailSize - Size of the thumbnail ('small', 'medium', 'large')
 * @param {boolean} props.showDescription - Whether to show the video description
 * @param {boolean} props.showChannel - Whether to show channel information
 * @param {boolean} props.showActions - Whether to show action buttons
 * @param {Array} props.actionMenuItems - Custom action menu items
 * @param {boolean} props.showWatchedIndicator - Whether to show the watched indicator
 * @param {Object} props.watchProgress - User's watch progress data
 * @param {string} props.playlistId - ID of the playlist this video is in (optional)
 * @param {number} props.playlistIndex - Index of this video in the playlist (optional)
 */
const VideoCard = ({
  video: rawVideo,
  horizontal = false,
  thumbnailSize = 'medium',
  showDescription = false,
  showChannel = true,
  showActions = true,
  actionMenuItems = [],
  showWatchedIndicator = true,
  watchProgress = null,
  playlistId = null,
  playlistIndex = null
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  
  // State
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const [playlistDialogOpen, setPlaylistDialogOpen] = useState(false);

  // Check if loading state
  const isLoading = !rawVideo || !rawVideo._id;

  // Format the video URLs to ensure they're complete
  const video = isLoading ? {} : formatVideoUrls(rawVideo);
  
  const {
    _id,
    title,
    thumbnail,
    views,
    createdAt,
    duration,
    owner,
    displayAuthor,
    authorName,
    user: videoUser // Keep for backward compatibility
  } = video;

  // Determine the author information to display
  const authorInfo = {
    name: displayAuthor || authorName || videoUser?.fullName || videoUser?.username || owner?.fullName || owner?.username || 'Anonymous',
    avatar: owner?.avatar || videoUser?.avatar,
    id: owner?._id || videoUser?._id,
    username: owner?.username || videoUser?.username
  };

  // Calculate thumbnail height based on size and orientation
  const getThumbnailHeight = () => {
    if (horizontal) {
      switch (thumbnailSize) {
        case 'small': return 80;
        case 'large': return 180;
        default: return 120; // medium
      }
    } else {
      switch (thumbnailSize) {
        case 'small': return 120;
        case 'large': return 200;
        default: return 160; // medium
      }
    }
  };

  // Get watch URL with playlist parameters if needed
  const getWatchUrl = () => {
    const baseUrl = `/videos/${_id}`;
    if (playlistId) {
      return `${baseUrl}?playlist=${playlistId}&index=${playlistIndex || 0}`;
    }
    return baseUrl;
  };

  // Handle clicking on more button
  const handleMenuOpen = (event) => {
    event.stopPropagation();
    event.preventDefault();
    setMenuAnchorEl(event.currentTarget);
  };

  // Handle closing the menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Handle add to playlist
  const handleAddToPlaylist = () => {
    handleMenuClose();
    setPlaylistDialogOpen(true);
  };

  // Handle add to watch later
  const handleAddToWatchLater = () => {
    // Implementation will be added later
    handleMenuClose();
  };

  // Handle custom menu item click
  const handleMenuItemClick = (item) => {
    handleMenuClose();
    if (item.onClick) {
      item.onClick(video);
    }
  };

  // Navigate to channel page
  const handleChannelClick = (e, channelId) => {
    e.stopPropagation();
    e.preventDefault();
    navigate(`/channel/${channelId}`);
  };

  if (isLoading) {
    return (
      <Card
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: horizontal ? 'row' : 'column',
          borderRadius: 2,
          overflow: 'hidden',
          boxShadow: theme.shadows[2]
        }}
      >
        <Skeleton
          variant="rectangular"
          height={getThumbnailHeight()}
          width={horizontal ? getThumbnailHeight() * 1.77 : '100%'}
          sx={{ flexShrink: 0 }}
        />
        <Box sx={{ p: 2, width: '100%' }}>
          <Skeleton variant="text" width="90%" height={24} />
          <Skeleton variant="text" width="60%" height={20} />
          {showChannel && <Skeleton variant="text" width="40%" height={20} />}
        </Box>
      </Card>
    );
  }

  // Handle relative URLs for thumbnails
  const thumbnailUrl = video.thumbnail?.startsWith('http') 
    ? video.thumbnail 
    : video.thumbnail 
      ? `${STATIC_URL}${video.thumbnail}` 
      : 'https://placehold.co/480x270?text=No+Thumbnail';

  // Handle relative URLs for video files
  const videoUrl = video.videoFile?.startsWith('http')
    ? video.videoFile
    : video.videoFile
      ? `${STATIC_URL}${video.videoFile}`
      : '';

  // Handle relative URLs for avatars
  const avatarUrl = authorInfo.avatar?.startsWith('http')
    ? authorInfo.avatar
    : authorInfo.avatar
      ? `${STATIC_URL}${authorInfo.avatar}`
      : 'https://placehold.co/48x48?text=User';

  // Thumbnail with duration and watched indicators
  const renderThumbnail = () => (
    <Box sx={{ position: 'relative' }}>
      <CardMedia
        component="img"
        height={getThumbnailHeight()}
        image={thumbnailUrl}
        alt={title}
        sx={{
          objectFit: 'cover',
          backgroundColor: 'background.default',
          transition: 'all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          '&:hover': {
            transform: 'scale(1.05)',
          }
        }}
      />
      
      {/* Duration indicator */}
      {duration && (
        <Chip
          label={formatDuration(duration)}
          size="small"
          sx={{
            position: 'absolute',
            bottom: 8,
            right: 8,
            bgcolor: 'rgba(0, 0, 0, 0.7)',
            color: 'white',
            height: 24,
            '& .MuiChip-label': {
              px: 1,
              py: 0.5,
              fontSize: '0.75rem'
            }
          }}
        />
      )}
      
      {/* Watched indicator */}
      {showWatchedIndicator && user && video.isWatched && (
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            bgcolor: 'primary.main'
          }}
        />
      )}
      
      {/* Watch progress indicator */}
      {watchProgress && watchProgress.watchedPercentage > 0 && watchProgress.watchedPercentage < 95 && (
        <Box
          sx={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            width: '100%',
            height: '4px',
            bgcolor: 'rgba(255, 255, 255, 0.3)'
          }}
        >
          <Box
            sx={{
              height: '100%',
              width: `${watchProgress.watchedPercentage}%`,
              bgcolor: 'primary.main'
            }}
          />
        </Box>
      )}
    </Box>
  );

  // Video metadata (title, views, time)
  const renderMetadata = () => (
    <CardContent 
      sx={{ 
        p: horizontal ? 2 : '16px 16px 8px 16px',
        '&:last-child': { pb: horizontal ? 2 : '8px' }
      }}
    >
      <Typography
        variant={horizontal ? 'subtitle1' : 'body1'}
        component="h3"
        sx={{
          fontWeight: 500,
          mb: 0.5,
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical'
        }}
      >
        {title}
      </Typography>

      {showDescription && video.description && (
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical',
            mb: 1
          }}
        >
          {video.description}
        </Typography>
      )}

      <Stack 
        direction="row" 
        spacing={1} 
        alignItems="center" 
        sx={{ 
          mt: 0.5,
          color: 'text.secondary',
          fontSize: '0.875rem'
        }}
      >
        <Typography variant="body2" component="span">
          {views.toLocaleString()} views
        </Typography>
        <Box sx={{ width: '4px', height: '4px', borderRadius: '50%', bgcolor: 'text.disabled' }} />
        <Typography variant="body2" component="span">
          {formatRelativeTime(createdAt)}
        </Typography>
      </Stack>

      {showChannel && (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            mt: 1,
            cursor: authorInfo.id ? 'pointer' : 'default'
          }}
          onClick={authorInfo.id ? (e) => handleChannelClick(e, authorInfo.id) : undefined}
        >
          <Avatar
            src={avatarUrl}
            alt={authorInfo.name}
            sx={{ width: 24, height: 24, mr: 1 }}
          />
          <Typography
            variant="body2"
            color="text.secondary"
            sx={{
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              '&:hover': authorInfo.id ? { color: 'primary.main' } : {}
            }}
          >
            {authorInfo.name}
          </Typography>
          {(videoUser?.isVerified || owner?.isVerified) && (
            <Tooltip title="Verified">
              <CheckCircleIcon
                fontSize="inherit"
                color="primary"
                sx={{ ml: 0.5, fontSize: 14 }}
              />
            </Tooltip>
          )}
        </Box>
      )}
    </CardContent>
  );

  return (
    <>
      <Card
        className="animate-fade-in-up transform-hover"
        sx={{
          height: '100%',
          display: 'flex',
          flexDirection: horizontal ? 'row' : 'column',
          borderRadius: 3,
          overflow: 'hidden',
          position: 'relative',
          transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            height: '3px',
            background: theme => theme.palette.mode === 'dark'
              ? `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              : `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            opacity: 0,
            transition: 'opacity 0.3s ease',
          },
          '&:hover': {
            transform: 'translateY(-8px) scale(1.02)',
            boxShadow: theme => theme.palette.mode === 'dark'
              ? '0 20px 60px rgba(0, 0, 0, 0.4)'
              : '0 20px 60px rgba(74, 20, 140, 0.15)',
            '&::before': {
              opacity: 1,
            },
          }
        }}
      >
        <CardActionArea
          component={RouterLink}
          to={getWatchUrl()}
          sx={{
            display: 'flex',
            flexDirection: horizontal ? 'row' : 'column',
            alignItems: 'stretch',
            height: '100%'
          }}
        >
          {renderThumbnail()}
          {renderMetadata()}
        </CardActionArea>

        {/* Actions menu button */}
        {showActions && user && (
          <Box
            sx={{
              position: 'absolute',
              top: 8,
              right: 8,
              zIndex: 1
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <IconButton
              aria-label="more options"
              onClick={handleMenuOpen}
              sx={{
                bgcolor: 'rgba(0, 0, 0, 0.5)',
                color: 'white',
                '&:hover': { bgcolor: 'rgba(0, 0, 0, 0.7)' },
                width: 32,
                height: 32
              }}
            >
              <MoreVertIcon fontSize="small" />
            </IconButton>
          </Box>
        )}
      </Card>

      {/* Action menu */}
      <Menu
        anchorEl={menuAnchorEl}
        open={Boolean(menuAnchorEl)}
        onClose={handleMenuClose}
        onClick={(e) => e.stopPropagation()}
        PaperProps={{
          sx: {
            width: 200,
            maxWidth: '100%'
          }
        }}
      >
        <MenuItem onClick={handleAddToPlaylist}>
          <ListItemIcon>
            <AddToPlaylistIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Add to playlist</ListItemText>
        </MenuItem>
        
        <MenuItem onClick={handleAddToWatchLater}>
          <ListItemIcon>
            <WatchLaterIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Watch later</ListItemText>
        </MenuItem>
        
        {/* Custom action items */}
        {actionMenuItems.map((item, index) => (
          <MenuItem key={index} onClick={() => handleMenuItemClick(item)}>
            {item.icon && <ListItemIcon>{item.icon}</ListItemIcon>}
            <ListItemText>{item.text}</ListItemText>
          </MenuItem>
        ))}
      </Menu>

      {/* Add to playlist dialog */}
      <AddToPlaylistDialog
        open={playlistDialogOpen}
        onClose={() => setPlaylistDialogOpen(false)}
        videoId={_id}
      />
    </>
  );
};

export default VideoCard; 